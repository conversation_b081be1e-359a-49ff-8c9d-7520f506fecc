def is_prime(num):
    if num < 2:
        return False
    if num == 2:
        return True
    if num % 2 == 0:
        return False

    for i in range(3, int(num ** 0.5) + 1, 2):
        if num % i == 0:
            return False
    return True

def nth_prime(n):
    count = 0
    num = 2

    while count < n:
        if is_prime(num):
            count += 1
            if count == n:
                return num
        num += 1

def collatz(n):
    steps = [n]
    while n != 1:
        if n % 2 == 0:
            n = n // 2
        else:
            n = 3 * n + 1
        steps.append(n)
    return steps

def sort_people(people):
    sorted_people = []
    for person in people:
        inserted = False
        for i in range(len(sorted_people)):
            if person[1] < sorted_people[i][1]:
                sorted_people.insert(i, person)
                inserted = True
                break
        if not inserted:
            sorted_people.append(person)
    return sorted_people

print(nth_prime(1))
print(nth_prime(5))
print(nth_prime(10))

print(collatz(nth_prime(3)))
print(collatz(nth_prime(7)))

people = [["fen", 20], ["bart", 25], ["alice", 18], ["bob", 30]]
print(sort_people(people))

collatz_results = collatz(nth_prime(4))
print(sort_people([["step" + str(i), step] for i, step in enumerate(collatz_results)]))