def is_prime(num):
    if num < 2:
        return False
    if num == 2:
        return True
    if num % 2 == 0:
        return False

    for i in range(3, int(num ** 0.5) + 1, 2):
        if num % i == 0:
            return False
    return True

def nth_prime(n):
    count = 0
    num = 2

    while count < n:
        if is_prime(num):
            count += 1
            if count == n:
                return num
        num += 1

def collatz(n):
    steps = [n]
    while n != 1:
        if n % 2 == 0:
            n = n // 2
        else:
            n = 3 * n + 1
        steps.append(n)
    return steps

def sort_people(people):
    for i in range(len(people)):
        for j in range(len(people) - 1):
            if people[j][1] > people[j + 1][1]:
                people[j], people[j + 1] = people[j + 1], people[j]
    return people

def sum_first_n(n):
    if n <= 0:
        return 0
    return n + sum_first_n(n - 1)

def combine_dicts(dict_list):
    result = {}
    for dictionary in dict_list:
        for key in dictionary:
            result[key] = dictionary[key]
    return result

def break_string(text, limit):
    result = []
    current = ""
    words = text.split(" ")

    for word in words:
        if len(current + word) <= limit:
            if current == "":
                current = word
            else:
                current = current + " " + word
        else:
            if current != "":
                result.append(current)
            current = word

    if current != "":
        result.append(current)

    return result

print(nth_prime(1))
print(nth_prime(5))
print(nth_prime(10))

print(collatz(nth_prime(3)))
print(collatz(nth_prime(7)))

people = [["fen", 20], ["bart", 25], ["alice", 18], ["bob", 30]]
print(sort_people(people))

collatz_results = collatz(nth_prime(4))
print(sort_people([["step" + str(i), step] for i, step in enumerate(collatz_results)]))

print(sum_first_n(5))
print(sum_first_n(10))

dicts = [{"a": 1, "b": 2}, {"b": 3, "c": 4}, {"a": 5, "d": 6}]
print(combine_dicts(dicts))