# vowels = 'aeiou'
# def is_vowel(char):
#     char = char.lower().strip()
#     return sum(1 for c in char if c in vowels)
# print(is_vowel('hello jason, there are 5 vowels in this sentence.'))

# def is_consonant(char):
#     char = char.lower().strip()
#     return sum(1 for c in char if c not in vowels)
# print(is_consonant('hello'))

# def count_duck(string):
#     words = string.strip()
#     words = words.split()
#     count = 0
#     for word in words:
#         if "ducks" in word.lower():
#             count += 2
#         elif "duck" in word.lower():
#             count += 1
#         else:
#             continue
#     return count

# print(count_duck("How many ducks in duck!"))
# print(count_duck("ducks in duck that is a duck"))

# def fib(n):
#     if n <= 0:
#         return 0
#     elif n == 1:
#         return 0
#     elif n == 2:
#         return 1
    
#     a, b = 0, 1
#     for i in range(n - 2):
#         a, b = b, a + b
#     return b

# print(fib(3))